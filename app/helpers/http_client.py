import asyncio
from typing import Optional

import httpx

from app.config import (
    HTTP_CONNECT_TIMEOUT_SECONDS,
    HTTP_MAX_REDIRECTS,
    HTTP_POOL_CONNECTIONS,
    HTTP_POOL_MAXSIZE,
    HTTP_READ_TIMEOUT_SECONDS,
    HTTP_TIMEOUT_SECONDS,
)
from app.logging import logger


class HTTPClientManager:
    """Singleton HTTP client manager with connection pooling for optimal performance."""

    _instance: Optional["HTTPClientManager"] = None
    _client: Optional[httpx.AsyncClient] = None
    _lock = asyncio.Lock()

    def __new__(cls) -> "HTTPClientManager":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_client(self) -> httpx.AsyncClient:
        """Get or create the async HTTP client with connection pooling."""
        if self._client is None:
            async with self._lock:
                if self._client is None:
                    await self._create_client()
        assert self._client is not None  # Should never be None after _create_client
        return self._client

    async def _create_client(self) -> None:
        """Create a new HTTP client with optimized settings."""
        # Configure timeouts
        timeout = httpx.Timeout(
            connect=HTTP_CONNECT_TIMEOUT_SECONDS,
            read=HTTP_READ_TIMEOUT_SECONDS,
            write=HTTP_TIMEOUT_SECONDS,
            pool=HTTP_TIMEOUT_SECONDS,
        )

        # Configure connection limits for optimal pooling
        limits = httpx.Limits(
            max_keepalive_connections=HTTP_POOL_CONNECTIONS,
            max_connections=HTTP_POOL_MAXSIZE,
            keepalive_expiry=30.0,  # Keep connections alive for 30 seconds
        )

        # Create client with optimized settings
        self._client = httpx.AsyncClient(
            timeout=timeout,
            limits=limits,
            follow_redirects=True,
            max_redirects=HTTP_MAX_REDIRECTS,
            headers={
                "User-Agent": "Face-Validation-Service/1.0",
            },
        )

        logger.info(
            f"HTTP client initialized with connection pooling: "
            f"max_connections={HTTP_POOL_MAXSIZE}, "
            f"max_keepalive={HTTP_POOL_CONNECTIONS}, "
            f"timeout={HTTP_TIMEOUT_SECONDS}s"
        )

    async def close(self) -> None:
        """Close the HTTP client and clean up connections."""
        if self._client is not None:
            await self._client.aclose()
            self._client = None
            logger.info("HTTP client closed")

    async def __aenter__(self):
        """Async context manager entry."""
        return await self.get_client()

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        # Don't close the client here as it's a singleton
        # It will be closed during application shutdown
        # Suppress unused parameter warnings
        _ = exc_type, exc_val, exc_tb


# Global instance
_http_client_manager: Optional[HTTPClientManager] = None


def get_http_client_manager() -> HTTPClientManager:
    """Get the global HTTP client manager instance."""
    global _http_client_manager
    if _http_client_manager is None:
        _http_client_manager = HTTPClientManager()
    return _http_client_manager


async def get_http_client() -> httpx.AsyncClient:
    """Get the global HTTP client with connection pooling."""
    manager = get_http_client_manager()
    return await manager.get_client()


async def close_http_client() -> None:
    """Close the global HTTP client."""
    global _http_client_manager
    if _http_client_manager is not None:
        await _http_client_manager.close()
        _http_client_manager = None
