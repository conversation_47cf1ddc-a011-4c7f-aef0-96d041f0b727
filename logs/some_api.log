2025-05-29 16:21:37.090 | INFO     | uvicorn.server:_serve:83 - Started server process [367455]
2025-05-29 16:21:37.091 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:21:37.091 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:21:37.091 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:21:37.091 | INFO     | app.helpers.face_detector:_get_face_detector:215 - Creating global face detector instance
2025-05-29 16:21:37.091 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:21:38.129 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.04s, detection size: (640, 640))
2025-05-29 16:21:38.213 | INFO     | app.helpers.face_detector:_warmup_model:103 - Model warmup completed successfully
2025-05-29 16:21:38.214 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:21:38.214 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:21:42.638 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:42.638 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:42.752 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:21:42.752 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:43.178 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 16:21:43.178 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:21:43.283 | INFO     | app.helpers.face_detector:detect_faces:181 - Detected 1 face(s) in image
2025-05-29 16:21:43.283 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:21:43.284 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:21:43.284 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:53572 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:21:46.462 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:21:46.462 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:21:47.682 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:21:47.683 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:21:49.245 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2702, 2674, 3)
2025-05-29 16:21:49.245 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:21:49.369 | INFO     | app.helpers.face_detector:detect_faces:183 - No faces detected in image
2025-05-29 16:21:49.369 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:21:49.370 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:21:49.370 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:53572 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fcamo.githubusercontent.com%2F8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e%2F68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 HTTP/1.1" 200
2025-05-29 16:21:52.170 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:52.170 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:52.218 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:21:52.218 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:52.616 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 16:21:52.616 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:21:52.774 | INFO     | app.helpers.face_detector:detect_faces:181 - Detected 1 face(s) in image
2025-05-29 16:21:52.774 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:21:52.775 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:21:52.776 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:53572 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:21:58.669 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://www.google.com/
2025-05-29 16:21:58.669 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://www.google.com/
2025-05-29 16:21:58.781 | WARNING  | app.services.face_validation_service:validate_face_from_url:36 - URL validation failed for sdfsdfsd: URL does not point to an image (content-type: text/html; charset=iso-8859-1)
2025-05-29 16:21:58.781 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:21:58.782 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:51416 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fwww.google.com HTTP/1.1" 200
2025-05-29 16:22:17.373 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://st.adda247.com/https://wpassets.adda247.com/wp-content/uploads/multisite/sites/5/2022/04/13080458/modi-6-16487841703x2-1.jpg
2025-05-29 16:22:17.373 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://st.adda247.com/https://wpassets.adda247.com/wp-content/uploads/multisite/sites/5/2022/04/13080458/modi-6-16487841703x2-1.jpg
2025-05-29 16:22:17.538 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:22:17.538 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://st.adda247.com/https://wpassets.adda247.com/wp-content/uploads/multisite/sites/5/2022/04/13080458/modi-6-16487841703x2-1.jpg
2025-05-29 16:22:17.903 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (800, 1200, 3)
2025-05-29 16:22:17.904 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:22:17.987 | INFO     | app.helpers.face_detector:detect_faces:181 - Detected 1 face(s) in image
2025-05-29 16:22:17.987 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:22:17.987 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:22:17.987 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:56490 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fst.adda247.com%2Fhttps%3A%2F%2Fwpassets.adda247.com%2Fwp-content%2Fuploads%2Fmultisite%2Fsites%2F5%2F2022%2F04%2F13080458%2Fmodi-6-16487841703x2-1.jpg HTTP/1.1" 200
2025-05-29 16:22:30.054 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/OIP._KepakEBIlgIkeSOnMdyyQHaHa?rs=1&pid=ImgDetMain
2025-05-29 16:22:30.054 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/OIP._KepakEBIlgIkeSOnMdyyQHaHa?rs=1&pid=ImgDetMain
2025-05-29 16:22:30.299 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:22:30.300 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/OIP._KepakEBIlgIkeSOnMdyyQHaHa?rs=1&pid=ImgDetMain
2025-05-29 16:22:30.516 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (474, 474, 3)
2025-05-29 16:22:30.517 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:22:30.597 | INFO     | app.helpers.face_detector:detect_faces:181 - Detected 1 face(s) in image
2025-05-29 16:22:30.598 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:22:30.598 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:22:30.598 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:56412 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FOIP._KepakEBIlgIkeSOnMdyyQHaHa%3Frs%3D1%26pid%3DImgDetMain HTTP/1.1" 200
2025-05-29 16:22:42.624 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://images.examples.com/wp-content/uploads/2024/05/Object.png
2025-05-29 16:22:42.624 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://images.examples.com/wp-content/uploads/2024/05/Object.png
2025-05-29 16:22:42.694 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:22:42.694 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://images.examples.com/wp-content/uploads/2024/05/Object.png
2025-05-29 16:22:42.790 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (675, 1200, 3)
2025-05-29 16:22:42.790 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:22:42.876 | INFO     | app.helpers.face_detector:detect_faces:183 - No faces detected in image
2025-05-29 16:22:42.876 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:22:42.876 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:22:42.876 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:43026 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fimages.examples.com%2Fwp-content%2Fuploads%2F2024%2F05%2FObject.png HTTP/1.1" 200
2025-05-29 16:22:52.737 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://vocabularyan.com/wp-content/uploads/2023/03/100-Classroom-Object-Names-with-Picture-.png
2025-05-29 16:22:52.737 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://vocabularyan.com/wp-content/uploads/2023/03/100-Classroom-Object-Names-with-Picture-.png
2025-05-29 16:22:52.814 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:22:52.814 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://vocabularyan.com/wp-content/uploads/2023/03/100-Classroom-Object-Names-with-Picture-.png
2025-05-29 16:22:52.894 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (480, 960, 3)
2025-05-29 16:22:52.894 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:22:53.059 | INFO     | app.helpers.face_detector:detect_faces:183 - No faces detected in image
2025-05-29 16:22:53.059 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:22:53.059 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:22:53.059 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:45356 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fvocabularyan.com%2Fwp-content%2Fuploads%2F2023%2F03%2F100-Classroom-Object-Names-with-Picture-.png HTTP/1.1" 200
2025-05-29 16:22:54.849 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:45356 - "GET /docs HTTP/1.1" 200
2025-05-29 16:22:54.934 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:45356 - "GET /openapi.json HTTP/1.1" 200
2025-05-29 16:23:30.440 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:23:30.540 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:23:30.540 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:23:30.541 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:23:30.541 | INFO     | uvicorn.server:_serve:93 - Finished server process [367455]
2025-05-29 16:23:31.381 | INFO     | uvicorn.server:_serve:83 - Started server process [368966]
2025-05-29 16:23:31.381 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:23:31.382 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:23:31.382 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:23:31.382 | INFO     | app.helpers.face_detector:_get_face_detector:215 - Creating global face detector instance
2025-05-29 16:23:31.382 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:23:32.060 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.68s, detection size: (640, 640))
2025-05-29 16:23:32.127 | INFO     | app.helpers.face_detector:_warmup_model:103 - Model warmup completed successfully
2025-05-29 16:23:32.128 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:23:32.128 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:23:33.643 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:23:33.745 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:23:33.745 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:23:33.745 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:23:33.745 | INFO     | uvicorn.server:_serve:93 - Finished server process [368966]
2025-05-29 16:30:19.957 | INFO     | uvicorn.server:_serve:83 - Started server process [373032]
2025-05-29 16:30:19.957 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:30:19.957 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:30:19.957 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:30:19.958 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:30:19.958 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:30:20.693 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.74s, detection size: (640, 640))
2025-05-29 16:30:20.784 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:30:20.784 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:30:20.784 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:30:42.175 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 960x480 (1.3MB)
2025-05-29 16:30:42.503 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:30:42.504 | INFO     | app.services.face_validation_service:validate_face_from_url:62 - Validation complete [sdfsd]: ✗ faces (0.49s)
2025-05-29 16:30:42.504 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:39276 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fvocabularyan.com%2Fwp-content%2Fuploads%2F2023%2F03%2F100-Classroom-Object-Names-with-Picture-.png HTTP/1.1" 200
2025-05-29 16:31:02.445 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 3586x3586 -> 1024x1024
2025-05-29 16:31:02.626 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 1024x1024 (3.0MB)
2025-05-29 16:31:02.712 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:31:02.712 | INFO     | app.services.face_validation_service:validate_face_from_url:62 - Validation complete [sdfsd]: ✓ faces (8.59s)
2025-05-29 16:31:02.712 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:57114 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DvjpIMLnhfgqZkQ%26riu%3Dhttp%253a%252f%252fwww.metropixie.com%252fwp-content%252fuploads%252f2016%252f02%252fDepositphotos_80959566_original.jpg%26ehk%3DlV0Fk1JVq%252flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%253d%26risl%3D1%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:32:20.717 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:32:20.818 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:32:20.819 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:32:20.819 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:32:20.819 | INFO     | uvicorn.server:_serve:93 - Finished server process [373032]
2025-05-29 16:32:21.687 | INFO     | uvicorn.server:_serve:83 - Started server process [374425]
2025-05-29 16:32:21.687 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:32:21.687 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:32:21.687 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:32:21.687 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:32:21.687 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:32:22.418 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.73s, detection size: (640, 640))
2025-05-29 16:32:22.527 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:32:22.528 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:32:22.528 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:32:23.134 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:32:23.237 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:32:23.237 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:32:23.237 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:32:23.237 | INFO     | uvicorn.server:_serve:93 - Finished server process [374425]
2025-05-29 16:32:46.253 | INFO     | uvicorn.server:_serve:83 - Started server process [374793]
2025-05-29 16:32:46.254 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:32:46.254 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:32:46.254 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:32:46.254 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:32:46.254 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:32:47.088 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.83s, detection size: (640, 640))
2025-05-29 16:32:47.415 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:32:47.417 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:32:47.418 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:32:49.187 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://th.bing.com/th/id/R.********************************?rik=vjpIMLnhfgqZkQ&riu=http%3a%2f%2fwww.metropixie.com%2fwp-content%2fuploads%2f2016%2f02%2fDepositphotos_80959566_original.jpg&ehk=lV0Fk1JVq%2flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%3d&risl=1&pid=ImgRaw&r=0
2025-05-29 16:32:49.777 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 3586x3586 -> 1024x1024
2025-05-29 16:32:49.957 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 1024x1024 (3.0MB)
2025-05-29 16:32:50.029 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:32:50.029 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (0.84s)
2025-05-29 16:32:50.029 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:49100 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DvjpIMLnhfgqZkQ%26riu%3Dhttp%253a%252f%252fwww.metropixie.com%252fwp-content%252fuploads%252f2016%252f02%252fDepositphotos_80959566_original.jpg%26ehk%3DlV0Fk1JVq%252flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%253d%26risl%3D1%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:32:54.366 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://th.bing.com/th/id/R.********************************?rik=vjpIMLnhfgqZkQ&riu=http%3a%2f%2fwww.metropixie.com%2fwp-content%2fuploads%2f2016%2f02%2fDepositphotos_80959566_original.jpg&ehk=lV0Fk1JVq%2flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%3d&risl=1&pid=ImgRaw&r=0
2025-05-29 16:32:55.014 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 3586x3586 -> 1024x1024
2025-05-29 16:32:55.174 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 1024x1024 (3.0MB)
2025-05-29 16:32:55.234 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:32:55.235 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (0.87s)
2025-05-29 16:32:55.235 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:49100 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DvjpIMLnhfgqZkQ%26riu%3Dhttp%253a%252f%252fwww.metropixie.com%252fwp-content%252fuploads%252f2016%252f02%252fDepositphotos_80959566_original.jpg%26ehk%3DlV0Fk1JVq%252flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%253d%26risl%3D1%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:33:02.667 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://th.bing.com/th/id/OIP.HFgIRFK8_DuDhZ7RgaqO8QHaHa?w=626&h=626&rs=1&pid=ImgDetMain
2025-05-29 16:33:02.941 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 626x626 (1.1MB)
2025-05-29 16:33:03.025 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:33:03.025 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (0.36s)
2025-05-29 16:33:03.026 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:51170 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FOIP.HFgIRFK8_DuDhZ7RgaqO8QHaHa%3Fw%3D626%26h%3D626%26rs%3D1%26pid%3DImgDetMain HTTP/1.1" 200
2025-05-29 16:34:03.620 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://th.bing.com/th/id/OIP.Dc8yFXYSTaVk5hS6peyBhgHaE8?rs=1&pid=ImgDetMain
2025-05-29 16:34:03.960 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 474x316 (0.4MB)
2025-05-29 16:34:04.029 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:34:04.030 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (0.41s)
2025-05-29 16:34:04.031 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:44408 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FOIP.Dc8yFXYSTaVk5hS6peyBhgHaE8%3Frs%3D1%26pid%3DImgDetMain HTTP/1.1" 200
2025-05-29 16:34:20.784 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://wallpaperaccess.com/full/2082932.jpg
2025-05-29 16:34:26.255 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 8688x5792 -> 1024x682
2025-05-29 16:34:26.826 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 1024x682 (2.0MB)
2025-05-29 16:34:26.904 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:34:26.904 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (6.12s)
2025-05-29 16:34:26.904 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:34058 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fwallpaperaccess.com%2Ffull%2F2082932.jpg HTTP/1.1" 200
2025-05-29 16:34:34.520 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:34:34.621 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:34:34.621 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:34:34.621 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:34:34.621 | INFO     | uvicorn.server:_serve:93 - Finished server process [374793]
2025-05-29 16:38:07.851 | INFO     | uvicorn.server:_serve:83 - Started server process [378318]
2025-05-29 16:38:07.851 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:38:07.851 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:38:07.851 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:38:07.851 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:38:07.851 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:38:08.636 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.78s, detection size: (640, 640))
2025-05-29 16:38:08.812 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:38:08.812 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:38:08.812 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:38:11.177 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://wallpaperaccess.com/full/2082932.jpg
2025-05-29 16:38:14.096 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 8688x5792 -> 1024x682
2025-05-29 16:38:14.523 | INFO     | app.helpers.image_downloader:download_image_to_memory:114 - Image loaded: 1024x682 (2.0MB)
2025-05-29 16:38:14.618 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:38:14.618 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (3.44s)
2025-05-29 16:38:14.619 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:50800 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fwallpaperaccess.com%2Ffull%2F2082932.jpg HTTP/1.1" 200
2025-05-29 16:39:33.331 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:39:33.431 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:39:33.432 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:39:33.432 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:39:33.432 | INFO     | uvicorn.server:_serve:93 - Finished server process [378318]
2025-05-29 16:47:05.881 | INFO     | __main__:main:174 - Starting cache functionality tests...
2025-05-29 16:47:05.881 | INFO     | __main__:test_cache_manager:21 - Testing cache manager...
2025-05-29 16:47:05.881 | INFO     | app.helpers.cache_manager:__init__:291 - Cache initialized: type=memory, max_size=500MB, ttl=3600s
2025-05-29 16:47:05.882 | INFO     | __main__:test_cache_manager:31 - Cache put success: True
2025-05-29 16:47:05.882 | INFO     | __main__:test_cache_manager:35 - Retrieved data: {'message': 'Hello, cache!'}
2025-05-29 16:47:05.882 | INFO     | __main__:test_cache_manager:39 - ✓ Cache manager basic functionality works
2025-05-29 16:47:05.882 | INFO     | __main__:test_cache_manager:45 - Cache stats: {'entries': 1, 'size_mb': 3.910064697265625e-05, 'max_size_mb': 500.0, 'utilization': 7.82012939453125e-08, 'enabled': True, 'type': 'memory'}
2025-05-29 16:47:05.882 | INFO     | __main__:test_image_download_caching:50 - Testing image download caching...
2025-05-29 16:47:05.882 | INFO     | __main__:test_image_download_caching:56 - First download (should hit network)...
2025-05-29 16:47:05.882 | INFO     | app.helpers.image_downloader:download_image_to_memory:55 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:47:07.152 | INFO     | app.helpers.image_downloader:download_image_to_memory:113 - Optimizing large download: 2674x2702 -> 1012x1024
2025-05-29 16:47:07.225 | INFO     | app.helpers.image_downloader:download_image_to_memory:136 - Image loaded: 1012x1024 (3.0MB)
2025-05-29 16:47:07.227 | INFO     | __main__:test_image_download_caching:65 - First download took: 1.345s
2025-05-29 16:47:07.227 | INFO     | __main__:test_image_download_caching:68 - Second download (should hit cache)...
2025-05-29 16:47:07.227 | INFO     | app.helpers.image_downloader:download_image_to_memory:51 - Image loaded from cache: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:47:07.227 | INFO     | __main__:test_image_download_caching:77 - Second download took: 0.000s
2025-05-29 16:47:07.228 | INFO     | __main__:test_image_download_caching:81 - ✓ Images are identical
2025-05-29 16:47:07.228 | INFO     | __main__:test_image_download_caching:87 - ✓ Cache hit detected (speedup: 10463.2x)
2025-05-29 16:47:07.228 | INFO     | __main__:test_face_detection_caching:94 - Testing face detection caching...
2025-05-29 16:47:07.229 | INFO     | __main__:test_face_detection_caching:100 - First face detection (should compute)...
2025-05-29 16:47:07.229 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:47:07.229 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:47:06.289 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: -0.94s, detection size: (640, 640))
2025-05-29 16:47:06.359 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:47:06.428 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:47:06.428 | INFO     | __main__:test_face_detection_caching:109 - First detection took: -0.800s, has_faces: False
2025-05-29 16:47:06.428 | INFO     | __main__:test_face_detection_caching:112 - Second face detection (should hit cache)...
2025-05-29 16:47:06.429 | INFO     | __main__:test_face_detection_caching:121 - Second detection took: 0.000s, has_faces: False
2025-05-29 16:47:06.429 | INFO     | __main__:test_face_detection_caching:125 - ✓ Detection results are identical
2025-05-29 16:47:06.429 | WARNING  | __main__:test_face_detection_caching:133 - ? Cache may not have been hit (times: -0.800s vs 0.000s)
2025-05-29 16:47:06.429 | INFO     | __main__:test_image_preprocessing_caching:138 - Testing image preprocessing caching...
2025-05-29 16:47:06.431 | INFO     | __main__:test_image_preprocessing_caching:144 - First preprocessing (should compute)...
2025-05-29 16:47:06.439 | INFO     | __main__:test_image_preprocessing_caching:149 - First preprocessing took: 0.008s
2025-05-29 16:47:06.440 | INFO     | __main__:test_image_preprocessing_caching:152 - Second preprocessing (should hit cache)...
2025-05-29 16:47:06.440 | INFO     | __main__:test_image_preprocessing_caching:157 - Second preprocessing took: 0.001s
2025-05-29 16:47:06.444 | INFO     | __main__:test_image_preprocessing_caching:161 - ✓ Processed images are identical
2025-05-29 16:47:06.444 | INFO     | __main__:test_image_preprocessing_caching:167 - ✓ Cache hit detected (speedup: 11.6x)
2025-05-29 16:47:06.444 | INFO     | __main__:main:192 - Final cache stats: {'entries': 5, 'size_mb': 4.596347808837891, 'max_size_mb': 500.0, 'utilization': 0.009192695617675781, 'enabled': True, 'type': 'memory'}
2025-05-29 16:47:06.444 | INFO     | __main__:main:194 - All cache tests completed!
2025-05-29 16:47:22.986 | INFO     | __main__:main:174 - Starting cache functionality tests...
2025-05-29 16:47:22.986 | INFO     | __main__:test_cache_manager:21 - Testing cache manager...
2025-05-29 16:47:22.986 | INFO     | app.helpers.cache_manager:__init__:291 - Cache initialized: type=memory, max_size=500MB, ttl=3600s
2025-05-29 16:47:22.986 | INFO     | __main__:test_cache_manager:31 - Cache put success: True
2025-05-29 16:47:22.986 | INFO     | __main__:test_cache_manager:35 - Retrieved data: {'message': 'Hello, cache!'}
2025-05-29 16:47:22.986 | INFO     | __main__:test_cache_manager:39 - ✓ Cache manager basic functionality works
2025-05-29 16:47:22.986 | INFO     | __main__:test_cache_manager:45 - Cache stats: {'entries': 1, 'size_mb': 3.910064697265625e-05, 'max_size_mb': 500.0, 'utilization': 7.82012939453125e-08, 'enabled': True, 'type': 'memory'}
2025-05-29 16:47:22.986 | INFO     | __main__:test_image_download_caching:50 - Testing image download caching...
2025-05-29 16:47:22.987 | INFO     | __main__:test_image_download_caching:56 - First download (should hit network)...
2025-05-29 16:47:22.987 | INFO     | app.helpers.image_downloader:download_image_to_memory:55 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:47:24.405 | INFO     | app.helpers.image_downloader:download_image_to_memory:113 - Optimizing large download: 2674x2702 -> 1012x1024
2025-05-29 16:47:24.470 | INFO     | app.helpers.image_downloader:download_image_to_memory:136 - Image loaded: 1012x1024 (3.0MB)
2025-05-29 16:47:24.471 | INFO     | __main__:test_image_download_caching:65 - First download took: 1.485s
2025-05-29 16:47:24.472 | INFO     | __main__:test_image_download_caching:68 - Second download (should hit cache)...
2025-05-29 16:47:24.472 | INFO     | app.helpers.image_downloader:download_image_to_memory:51 - Image loaded from cache: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:47:24.472 | INFO     | __main__:test_image_download_caching:77 - Second download took: 0.000s
2025-05-29 16:47:24.472 | INFO     | __main__:test_image_download_caching:81 - ✓ Images are identical
2025-05-29 16:47:24.472 | INFO     | __main__:test_image_download_caching:87 - ✓ Cache hit detected (speedup: 11364.0x)
2025-05-29 16:47:24.473 | INFO     | __main__:test_face_detection_caching:94 - Testing face detection caching...
2025-05-29 16:47:24.473 | INFO     | __main__:test_face_detection_caching:100 - First face detection (should compute)...
2025-05-29 16:47:24.473 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:47:24.473 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:47:25.081 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.61s, detection size: (640, 640))
2025-05-29 16:47:25.165 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:47:25.243 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:47:25.243 | INFO     | __main__:test_face_detection_caching:109 - First detection took: 0.770s, has_faces: False
2025-05-29 16:47:25.243 | INFO     | __main__:test_face_detection_caching:112 - Second face detection (should hit cache)...
2025-05-29 16:47:25.244 | INFO     | __main__:test_face_detection_caching:121 - Second detection took: 0.000s, has_faces: False
2025-05-29 16:47:25.244 | INFO     | __main__:test_face_detection_caching:125 - ✓ Detection results are identical
2025-05-29 16:47:25.244 | INFO     | __main__:test_face_detection_caching:131 - ✓ Cache hit detected (speedup: 2406.6x)
2025-05-29 16:47:25.244 | INFO     | __main__:test_image_preprocessing_caching:138 - Testing image preprocessing caching...
2025-05-29 16:47:25.247 | INFO     | __main__:test_image_preprocessing_caching:144 - First preprocessing (should compute)...
2025-05-29 16:47:25.256 | INFO     | __main__:test_image_preprocessing_caching:149 - First preprocessing took: 0.008s
2025-05-29 16:47:25.256 | INFO     | __main__:test_image_preprocessing_caching:152 - Second preprocessing (should hit cache)...
2025-05-29 16:47:25.257 | INFO     | __main__:test_image_preprocessing_caching:157 - Second preprocessing took: 0.001s
2025-05-29 16:47:25.262 | INFO     | __main__:test_image_preprocessing_caching:161 - ✓ Processed images are identical
2025-05-29 16:47:25.262 | INFO     | __main__:test_image_preprocessing_caching:167 - ✓ Cache hit detected (speedup: 11.4x)
2025-05-29 16:47:25.262 | INFO     | __main__:main:192 - Final cache stats: {'entries': 5, 'size_mb': 4.596347808837891, 'max_size_mb': 500.0, 'utilization': 0.009192695617675781, 'enabled': True, 'type': 'memory'}
2025-05-29 16:47:25.262 | INFO     | __main__:main:194 - All cache tests completed!
2025-05-29 16:56:50.427 | INFO     | test_caching:test_cache_manager:21 - Testing cache manager...
2025-05-29 16:56:50.427 | INFO     | app.helpers.cache_manager:__init__:312 - Cache initialized: type=memory, max_size=500MB, ttl=3600s
2025-05-29 16:56:50.427 | INFO     | test_caching:test_cache_manager:31 - Cache put success: True
2025-05-29 16:56:50.427 | INFO     | test_caching:test_cache_manager:35 - Retrieved data: {'message': 'Hello, cache!'}
2025-05-29 16:56:50.427 | INFO     | test_caching:test_cache_manager:39 - ✓ Cache manager basic functionality works
2025-05-29 16:56:50.427 | INFO     | test_caching:test_cache_manager:45 - Cache stats: {'entries': 1, 'size_mb': 3.910064697265625e-05, 'max_size_mb': 500.0, 'utilization': 7.82012939453125e-08, 'enabled': True, 'type': 'memory'}
2025-05-29 16:56:50.428 | INFO     | test_caching:test_image_download_caching:50 - Testing image download caching...
2025-05-29 16:56:50.428 | INFO     | test_caching:test_image_download_caching:56 - First download (should hit network)...
2025-05-29 16:56:50.464 | INFO     | test_caching:test_face_detection_caching:94 - Testing face detection caching...
2025-05-29 16:56:50.464 | INFO     | test_caching:test_face_detection_caching:100 - First face detection (should compute)...
2025-05-29 16:56:50.465 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:56:50.465 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:56:51.227 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.76s, detection size: (640, 640))
2025-05-29 16:56:51.329 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:56:51.427 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:51.427 | INFO     | test_caching:test_face_detection_caching:109 - First detection took: 0.963s, has_faces: False
2025-05-29 16:56:51.427 | INFO     | test_caching:test_face_detection_caching:112 - Second face detection (should hit cache)...
2025-05-29 16:56:51.427 | INFO     | test_caching:test_face_detection_caching:121 - Second detection took: 0.000s, has_faces: False
2025-05-29 16:56:51.427 | INFO     | test_caching:test_face_detection_caching:125 - ✓ Detection results are identical
2025-05-29 16:56:51.428 | INFO     | test_caching:test_face_detection_caching:131 - ✓ Cache hit detected (speedup: 5334.0x)
2025-05-29 16:56:51.429 | INFO     | test_caching:test_image_preprocessing_caching:138 - Testing image preprocessing caching...
2025-05-29 16:56:51.431 | INFO     | test_caching:test_image_preprocessing_caching:144 - First preprocessing (should compute)...
2025-05-29 16:56:51.439 | INFO     | test_caching:test_image_preprocessing_caching:149 - First preprocessing took: 0.008s
2025-05-29 16:56:51.439 | INFO     | test_caching:test_image_preprocessing_caching:152 - Second preprocessing (should hit cache)...
2025-05-29 16:56:51.440 | INFO     | test_caching:test_image_preprocessing_caching:157 - Second preprocessing took: 0.001s
2025-05-29 16:56:51.448 | INFO     | test_caching:test_image_preprocessing_caching:161 - ✓ Processed images are identical
2025-05-29 16:56:51.448 | INFO     | test_caching:test_image_preprocessing_caching:167 - ✓ Cache hit detected (speedup: 11.2x)
2025-05-29 16:56:51.500 | INFO     | app.helpers.image_processor:optimize_for_face_detection:234 - Resizing large image: 1536x2048 -> 768x1024 (75.0% reduction)
2025-05-29 16:56:51.509 | INFO     | app.helpers.image_processor:optimize_for_face_detection:234 - Resizing large image: 3000x4000 -> 768x1024 (93.4% reduction)
2025-05-29 16:56:51.771 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:56:52.285 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.51s, detection size: (640, 640))
2025-05-29 16:56:52.346 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:56:52.411 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:52.412 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:56:53.043 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.63s, detection size: (640, 640))
2025-05-29 16:56:53.129 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:56:53.194 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:53.276 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:53.339 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:53.401 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:53.463 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:53.524 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:53.531 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:56:53.531 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:56:53.532 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:56:54.785 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.25s, detection size: (640, 640))
2025-05-29 16:56:55.177 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.65s, detection size: (640, 640))
2025-05-29 16:56:55.250 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.72s, detection size: (640, 640))
2025-05-29 16:56:55.252 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:56:55.408 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:56:55.571 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:56:55.742 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:55.798 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:56:55.817 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:58:58.468 | INFO     | test_caching:test_cache_manager:25 - Testing cache manager...
2025-05-29 16:58:58.468 | INFO     | app.helpers.cache_manager:__init__:312 - Cache initialized: type=memory, max_size=500MB, ttl=3600s
2025-05-29 16:58:58.468 | INFO     | test_caching:test_cache_manager:35 - Cache put success: True
2025-05-29 16:58:58.468 | INFO     | test_caching:test_cache_manager:39 - Retrieved data: {'message': 'Hello, cache!'}
2025-05-29 16:58:58.468 | INFO     | test_caching:test_cache_manager:43 - ✓ Cache manager basic functionality works
2025-05-29 16:58:58.468 | INFO     | test_caching:test_cache_manager:49 - Cache stats: {'entries': 1, 'size_mb': 3.910064697265625e-05, 'max_size_mb': 500.0, 'utilization': 7.82012939453125e-08, 'enabled': True, 'type': 'memory'}
2025-05-29 16:58:58.470 | INFO     | test_caching:test_face_detection_caching:100 - Testing face detection caching...
2025-05-29 16:58:58.471 | INFO     | test_caching:test_face_detection_caching:106 - First face detection (should compute)...
2025-05-29 16:58:58.471 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:58:58.471 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:58:59.131 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.66s, detection size: (640, 640))
2025-05-29 16:58:59.195 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:58:59.266 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:58:59.266 | INFO     | test_caching:test_face_detection_caching:115 - First detection took: 0.796s, has_faces: False
2025-05-29 16:58:59.266 | INFO     | test_caching:test_face_detection_caching:118 - Second face detection (should hit cache)...
2025-05-29 16:58:59.267 | INFO     | test_caching:test_face_detection_caching:127 - Second detection took: 0.000s, has_faces: False
2025-05-29 16:58:59.267 | INFO     | test_caching:test_face_detection_caching:131 - ✓ Detection results are identical
2025-05-29 16:58:59.267 | INFO     | test_caching:test_face_detection_caching:137 - ✓ Cache hit detected (speedup: 4533.5x)
2025-05-29 16:58:59.268 | INFO     | test_caching:test_image_preprocessing_caching:146 - Testing image preprocessing caching...
2025-05-29 16:58:59.270 | INFO     | test_caching:test_image_preprocessing_caching:152 - First preprocessing (should compute)...
2025-05-29 16:58:59.278 | INFO     | test_caching:test_image_preprocessing_caching:157 - First preprocessing took: 0.008s
2025-05-29 16:58:59.278 | INFO     | test_caching:test_image_preprocessing_caching:160 - Second preprocessing (should hit cache)...
2025-05-29 16:58:59.279 | INFO     | test_caching:test_image_preprocessing_caching:165 - Second preprocessing took: 0.001s
2025-05-29 16:58:59.282 | INFO     | test_caching:test_image_preprocessing_caching:169 - ✓ Processed images are identical
2025-05-29 16:58:59.283 | INFO     | test_caching:test_image_preprocessing_caching:175 - ✓ Cache hit detected (speedup: 11.6x)
2025-05-29 16:58:59.325 | INFO     | app.helpers.image_processor:optimize_for_face_detection:234 - Resizing large image: 1536x2048 -> 768x1024 (75.0% reduction)
2025-05-29 16:58:59.332 | INFO     | app.helpers.image_processor:optimize_for_face_detection:234 - Resizing large image: 3000x4000 -> 768x1024 (93.4% reduction)
2025-05-29 16:58:59.542 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:58:59.973 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.43s, detection size: (640, 640))
2025-05-29 16:59:00.034 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:59:00.094 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:00.094 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:59:00.593 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.50s, detection size: (640, 640))
2025-05-29 16:59:00.655 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:59:00.723 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:00.787 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:00.868 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:00.953 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:01.056 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:01.147 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:01.153 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:59:01.153 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:59:01.154 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:59:02.576 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.42s, detection size: (640, 640))
2025-05-29 16:59:02.857 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.70s, detection size: (640, 640))
2025-05-29 16:59:02.937 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.78s, detection size: (640, 640))
2025-05-29 16:59:03.166 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:59:03.301 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:59:03.632 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:59:03.751 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:03.850 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:59:03.912 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:31.471 | INFO     | test_caching:test_cache_manager:26 - Testing cache manager...
2025-05-29 17:00:31.471 | INFO     | app.helpers.cache_manager:__init__:312 - Cache initialized: type=memory, max_size=500MB, ttl=3600s
2025-05-29 17:00:31.471 | INFO     | test_caching:test_cache_manager:36 - Cache put success: True
2025-05-29 17:00:31.471 | INFO     | test_caching:test_cache_manager:40 - Retrieved data: {'message': 'Hello, cache!'}
2025-05-29 17:00:31.471 | INFO     | test_caching:test_cache_manager:44 - ✓ Cache manager basic functionality works
2025-05-29 17:00:31.471 | INFO     | test_caching:test_cache_manager:50 - Cache stats: {'entries': 1, 'size_mb': 3.910064697265625e-05, 'max_size_mb': 500.0, 'utilization': 7.82012939453125e-08, 'enabled': True, 'type': 'memory'}
2025-05-29 17:00:31.472 | INFO     | test_caching:test_image_download_caching:56 - Testing image download caching...
2025-05-29 17:00:31.473 | INFO     | test_caching:test_image_download_caching:62 - First download (should hit network)...
2025-05-29 17:00:31.473 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 17:00:31.566 | INFO     | app.helpers.http_client:_create_client:66 - HTTP client initialized with connection pooling: max_connections=20, max_keepalive=10, timeout=30s
2025-05-29 17:00:33.326 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 "HTTP/1.1 200 OK"
2025-05-29 17:00:33.366 | INFO     | app.helpers.image_downloader:download_image_to_memory:123 - Optimizing large download: 2674x2702 -> 1012x1024
2025-05-29 17:00:33.444 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 1012x1024 (3.0MB)
2025-05-29 17:00:33.446 | INFO     | test_caching:test_image_download_caching:71 - First download took: 1.973s
2025-05-29 17:00:33.446 | INFO     | test_caching:test_image_download_caching:74 - Second download (should hit cache)...
2025-05-29 17:00:33.446 | INFO     | app.helpers.image_downloader:download_image_to_memory:52 - Image loaded from cache: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 17:00:33.446 | INFO     | test_caching:test_image_download_caching:83 - Second download took: 0.000s
2025-05-29 17:00:33.447 | INFO     | test_caching:test_image_download_caching:87 - ✓ Images are identical
2025-05-29 17:00:33.447 | INFO     | test_caching:test_image_download_caching:93 - ✓ Cache hit detected (speedup: 9414.0x)
2025-05-29 17:00:33.448 | INFO     | test_caching:test_face_detection_caching:102 - Testing face detection caching...
2025-05-29 17:00:33.449 | INFO     | test_caching:test_face_detection_caching:108 - First face detection (should compute)...
2025-05-29 17:00:33.449 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 17:00:33.449 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:00:34.398 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.95s, detection size: (640, 640))
2025-05-29 17:00:34.550 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:00:34.711 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:34.711 | INFO     | test_caching:test_face_detection_caching:117 - First detection took: 1.262s, has_faces: False
2025-05-29 17:00:34.711 | INFO     | test_caching:test_face_detection_caching:120 - Second face detection (should hit cache)...
2025-05-29 17:00:34.712 | INFO     | test_caching:test_face_detection_caching:129 - Second detection took: 0.000s, has_faces: False
2025-05-29 17:00:34.712 | INFO     | test_caching:test_face_detection_caching:133 - ✓ Detection results are identical
2025-05-29 17:00:34.713 | INFO     | test_caching:test_face_detection_caching:139 - ✓ Cache hit detected (speedup: 5022.6x)
2025-05-29 17:00:34.715 | INFO     | test_caching:test_image_preprocessing_caching:148 - Testing image preprocessing caching...
2025-05-29 17:00:34.717 | INFO     | test_caching:test_image_preprocessing_caching:154 - First preprocessing (should compute)...
2025-05-29 17:00:34.728 | INFO     | test_caching:test_image_preprocessing_caching:159 - First preprocessing took: 0.010s
2025-05-29 17:00:34.730 | INFO     | test_caching:test_image_preprocessing_caching:162 - Second preprocessing (should hit cache)...
2025-05-29 17:00:34.732 | INFO     | test_caching:test_image_preprocessing_caching:167 - Second preprocessing took: 0.002s
2025-05-29 17:00:34.737 | INFO     | test_caching:test_image_preprocessing_caching:171 - ✓ Processed images are identical
2025-05-29 17:00:34.737 | INFO     | test_caching:test_image_preprocessing_caching:177 - ✓ Cache hit detected (speedup: 5.0x)
2025-05-29 17:00:34.808 | INFO     | app.helpers.image_processor:optimize_for_face_detection:234 - Resizing large image: 1536x2048 -> 768x1024 (75.0% reduction)
2025-05-29 17:00:34.818 | INFO     | app.helpers.image_processor:optimize_for_face_detection:234 - Resizing large image: 3000x4000 -> 768x1024 (93.4% reduction)
2025-05-29 17:00:35.102 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://picsum.photos/400/300
2025-05-29 17:00:36.105 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://picsum.photos/400/300 "HTTP/1.1 302 Found"
2025-05-29 17:00:36.146 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://fastly.picsum.photos/id/849/400/300.jpg?hmac=wcE8UrMqBx4ELkDOP12fGToUPSsPNOJOJP0JLpSkF6Y "HTTP/1.1 200 OK"
2025-05-29 17:00:36.148 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 400x300 (0.3MB)
2025-05-29 17:00:36.150 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://picsum.photos/1200/800
2025-05-29 17:00:36.480 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://picsum.photos/1200/800 "HTTP/1.1 302 Found"
2025-05-29 17:00:37.591 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://fastly.picsum.photos/id/580/1200/800.jpg?hmac=JknQ7ihodiHBLJre1I1CiM_SkYo-F8si0HPtUPkbBVs "HTTP/1.1 200 OK"
2025-05-29 17:00:37.608 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 1024x682 (2.0MB)
2025-05-29 17:00:37.615 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://picsum.photos/2400/1600
2025-05-29 17:00:38.614 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://picsum.photos/2400/1600 "HTTP/1.1 302 Found"
2025-05-29 17:00:40.361 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://fastly.picsum.photos/id/1079/2400/1600.jpg?hmac=AqKcJ-D4MtqN0aJ8ih4kC6mdQ_hQf_1N_6ThI0evR7c "HTTP/1.1 200 OK"
2025-05-29 17:00:40.385 | INFO     | app.helpers.image_downloader:download_image_to_memory:123 - Optimizing large download: 2400x1600 -> 1024x682
2025-05-29 17:00:40.414 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 1024x682 (2.0MB)
2025-05-29 17:00:40.422 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:00:41.052 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.63s, detection size: (640, 640))
2025-05-29 17:00:41.122 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:00:41.191 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:41.191 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:00:41.736 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.55s, detection size: (640, 640))
2025-05-29 17:00:41.851 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:00:42.027 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:42.124 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:42.213 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:42.283 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:42.343 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:42.414 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:42.419 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:00:42.419 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:00:42.420 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:00:43.944 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.52s, detection size: (640, 640))
2025-05-29 17:00:43.946 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.53s, detection size: (640, 640))
2025-05-29 17:00:44.077 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.66s, detection size: (640, 640))
2025-05-29 17:00:44.151 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:00:44.337 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:00:44.484 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:44.585 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:00:44.607 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:00:44.672 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:30.900 | INFO     | test_caching:test_cache_manager:26 - Testing cache manager...
2025-05-29 17:01:30.901 | INFO     | app.helpers.cache_manager:__init__:312 - Cache initialized: type=memory, max_size=500MB, ttl=3600s
2025-05-29 17:01:30.901 | INFO     | test_caching:test_cache_manager:36 - Cache put success: True
2025-05-29 17:01:30.901 | INFO     | test_caching:test_cache_manager:40 - Retrieved data: {'message': 'Hello, cache!'}
2025-05-29 17:01:30.901 | INFO     | test_caching:test_cache_manager:44 - ✓ Cache manager basic functionality works
2025-05-29 17:01:30.901 | INFO     | test_caching:test_cache_manager:50 - Cache stats: {'entries': 1, 'size_mb': 3.910064697265625e-05, 'max_size_mb': 500.0, 'utilization': 7.82012939453125e-08, 'enabled': True, 'type': 'memory'}
2025-05-29 17:01:30.902 | INFO     | test_caching:test_image_download_caching:56 - Testing image download caching...
2025-05-29 17:01:30.902 | INFO     | test_caching:test_image_download_caching:62 - First download (should hit network)...
2025-05-29 17:01:30.902 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 17:01:30.936 | INFO     | app.helpers.http_client:_create_client:66 - HTTP client initialized with connection pooling: max_connections=20, max_keepalive=10, timeout=30s
2025-05-29 17:01:31.597 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 "HTTP/1.1 200 OK"
2025-05-29 17:01:31.633 | INFO     | app.helpers.image_downloader:download_image_to_memory:123 - Optimizing large download: 2674x2702 -> 1012x1024
2025-05-29 17:01:31.686 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 1012x1024 (3.0MB)
2025-05-29 17:01:31.688 | INFO     | test_caching:test_image_download_caching:71 - First download took: 0.786s
2025-05-29 17:01:31.688 | INFO     | test_caching:test_image_download_caching:74 - Second download (should hit cache)...
2025-05-29 17:01:31.688 | INFO     | app.helpers.image_downloader:download_image_to_memory:52 - Image loaded from cache: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 17:01:31.688 | INFO     | test_caching:test_image_download_caching:83 - Second download took: 0.000s
2025-05-29 17:01:31.688 | INFO     | test_caching:test_image_download_caching:87 - ✓ Images are identical
2025-05-29 17:01:31.688 | INFO     | test_caching:test_image_download_caching:93 - ✓ Cache hit detected (speedup: 9637.0x)
2025-05-29 17:01:31.689 | INFO     | test_caching:test_face_detection_caching:102 - Testing face detection caching...
2025-05-29 17:01:31.690 | INFO     | test_caching:test_face_detection_caching:108 - First face detection (should compute)...
2025-05-29 17:01:31.690 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 17:01:31.690 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:01:32.302 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.61s, detection size: (640, 640))
2025-05-29 17:01:32.374 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:01:32.449 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:32.450 | INFO     | test_caching:test_face_detection_caching:117 - First detection took: 0.760s, has_faces: False
2025-05-29 17:01:32.450 | INFO     | test_caching:test_face_detection_caching:120 - Second face detection (should hit cache)...
2025-05-29 17:01:32.450 | INFO     | test_caching:test_face_detection_caching:129 - Second detection took: 0.000s, has_faces: False
2025-05-29 17:01:32.450 | INFO     | test_caching:test_face_detection_caching:133 - ✓ Detection results are identical
2025-05-29 17:01:32.450 | INFO     | test_caching:test_face_detection_caching:139 - ✓ Cache hit detected (speedup: 4178.8x)
2025-05-29 17:01:32.452 | INFO     | test_caching:test_image_preprocessing_caching:148 - Testing image preprocessing caching...
2025-05-29 17:01:32.454 | INFO     | test_caching:test_image_preprocessing_caching:154 - First preprocessing (should compute)...
2025-05-29 17:01:32.470 | INFO     | test_caching:test_image_preprocessing_caching:159 - First preprocessing took: 0.016s
2025-05-29 17:01:32.471 | INFO     | test_caching:test_image_preprocessing_caching:162 - Second preprocessing (should hit cache)...
2025-05-29 17:01:32.472 | INFO     | test_caching:test_image_preprocessing_caching:167 - Second preprocessing took: 0.001s
2025-05-29 17:01:32.475 | INFO     | test_caching:test_image_preprocessing_caching:171 - ✓ Processed images are identical
2025-05-29 17:01:32.475 | INFO     | test_caching:test_image_preprocessing_caching:177 - ✓ Cache hit detected (speedup: 18.5x)
2025-05-29 17:01:32.524 | INFO     | app.helpers.image_processor:optimize_for_face_detection:234 - Resizing large image: 1536x2048 -> 768x1024 (75.0% reduction)
2025-05-29 17:01:32.532 | INFO     | app.helpers.image_processor:optimize_for_face_detection:234 - Resizing large image: 3000x4000 -> 768x1024 (93.4% reduction)
2025-05-29 17:01:32.759 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://picsum.photos/400/300
2025-05-29 17:01:33.715 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://picsum.photos/400/300 "HTTP/1.1 302 Found"
2025-05-29 17:01:33.772 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://fastly.picsum.photos/id/233/400/300.jpg?hmac=9MrUtNnEmL2uPdW8gSlPC_u30uMylQQAKGE7YVaUnDo "HTTP/1.1 200 OK"
2025-05-29 17:01:33.781 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 400x300 (0.3MB)
2025-05-29 17:01:33.783 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://picsum.photos/1200/800
2025-05-29 17:01:34.099 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://picsum.photos/1200/800 "HTTP/1.1 302 Found"
2025-05-29 17:01:35.376 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://fastly.picsum.photos/id/95/1200/800.jpg?hmac=JDKAhSvVMhgUSJGfz028yqZHeSNGPIc0ZPSfObJhosA "HTTP/1.1 200 OK"
2025-05-29 17:01:35.406 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 1024x682 (2.0MB)
2025-05-29 17:01:35.417 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://picsum.photos/2400/1600
2025-05-29 17:01:36.390 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://picsum.photos/2400/1600 "HTTP/1.1 302 Found"
2025-05-29 17:01:38.070 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://fastly.picsum.photos/id/634/2400/1600.jpg?hmac=17oJPSTyimW9Z5aDZuBhN0b6X_F30hFucURhX2g-MSA "HTTP/1.1 200 OK"
2025-05-29 17:01:38.085 | INFO     | app.helpers.image_downloader:download_image_to_memory:123 - Optimizing large download: 2400x1600 -> 1024x682
2025-05-29 17:01:38.108 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 1024x682 (2.0MB)
2025-05-29 17:01:38.117 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:01:38.755 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.64s, detection size: (640, 640))
2025-05-29 17:01:38.826 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:01:38.943 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:38.944 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:01:39.488 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.54s, detection size: (640, 640))
2025-05-29 17:01:39.706 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:01:39.849 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:40.045 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:40.116 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:40.180 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:40.241 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:40.325 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:40.370 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:01:40.371 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:01:40.371 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:01:41.721 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.35s, detection size: (640, 640))
2025-05-29 17:01:41.863 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.49s, detection size: (640, 640))
2025-05-29 17:01:42.049 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.68s, detection size: (640, 640))
2025-05-29 17:01:42.155 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:01:42.224 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:01:42.571 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:01:42.594 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:42.717 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:42.740 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 17:01:52.975 | INFO     | uvicorn.server:_serve:83 - Started server process [394361]
2025-05-29 17:01:52.975 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 17:01:52.976 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 17:01:52.976 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 17:01:52.976 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 17:01:52.976 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 17:01:53.610 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.63s, detection size: (640, 640))
2025-05-29 17:01:53.743 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 17:01:53.743 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 17:01:53.743 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 17:01:56.136 | INFO     | app.api.face_routes:validate_face:27 - Face validation request - ID: sdfsd, URL: https://wallpaperaccess.com/full/2082932.jpg
2025-05-29 17:01:56.161 | INFO     | app.helpers.http_client:_create_client:66 - HTTP client initialized with connection pooling: max_connections=20, max_keepalive=10, timeout=30s
2025-05-29 17:01:56.271 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: HEAD https://wallpaperaccess.com/full/2082932.jpg "HTTP/1.1 200 OK"
2025-05-29 17:01:56.271 | INFO     | app.helpers.cache_manager:__init__:312 - Cache initialized: type=memory, max_size=500MB, ttl=3600s
2025-05-29 17:01:56.271 | INFO     | app.helpers.image_downloader:download_image_to_memory:56 - Downloading image from: https://wallpaperaccess.com/full/2082932.jpg
2025-05-29 17:01:56.291 | INFO     | httpx._client:_send_single_request:1740 - HTTP Request: GET https://wallpaperaccess.com/full/2082932.jpg "HTTP/1.1 200 OK"
2025-05-29 17:01:56.363 | INFO     | app.helpers.image_downloader:download_image_to_memory:123 - Optimizing large download: 8688x5792 -> 1024x682
2025-05-29 17:01:56.966 | INFO     | app.helpers.image_downloader:download_image_to_memory:146 - Image loaded: 1024x682 (2.0MB)
2025-05-29 17:01:57.207 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 17:01:57.207 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (1.07s)
2025-05-29 17:01:57.209 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:33062 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fwallpaperaccess.com%2Ffull%2F2082932.jpg HTTP/1.1" 200
2025-05-29 17:02:26.752 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 17:02:26.853 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 17:02:26.853 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 17:02:26.854 | INFO     | app.helpers.http_client:close:78 - HTTP client closed
2025-05-29 17:02:26.854 | INFO     | main:lifespan:47 - HTTP client connections closed
2025-05-29 17:02:26.854 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 17:02:26.854 | INFO     | uvicorn.server:_serve:93 - Finished server process [394361]
