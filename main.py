from contextlib import asynccontextmanager

from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.api import routers
from app.config import (
    BUILD_DATE,
    DESCRIPTION,
    HOST,
    PORT,
    PRELOAD_MODEL,
    TITLE,
    VERSION,
)
from app.helpers import close_http_client, preload_face_detector
from app.logging import init_logger, logger

init_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle application startup and shutdown events."""
    # Startup
    logger.info("Starting face validation service...")

    # Preload the face detection model to avoid cold start delays
    if PRELOAD_MODEL:
        logger.info("Preloading face detection model...")
        if preload_face_detector():
            logger.info("Face detection model preloaded successfully")
        else:
            logger.warning(
                "Failed to preload face detection model - will load on first request"
            )
    else:
        logger.info("Model preloading disabled - model will load on first request")

    yield

    # Shutdown
    logger.info("Shutting down face validation service...")

    # Close HTTP client connections
    await close_http_client()
    logger.info("HTTP client connections closed")


app = FastAPI(
    title=TITLE,
    version=f"{VERSION}.{BUILD_DATE}",
    description=DESCRIPTION,
    swagger_ui_parameters={"defaultModelsExpandDepth": 0},
    lifespan=lifespan,
)

for router in routers:
    app.include_router(router)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host=HOST, port=PORT)
